<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Ajax extends \Theme25\ControllerSubMethods {

    public function execute() {
        $this->jsonResponse([
            'success' => true,
            'nothing_to_do' => true
        ]);
    }

    /**
     * Зарежда подкатегории чрез AJAX
     */
    public function loadSubcategories() {

        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            // Получаване на parent_id от заявката
            $parent_id = (int)$this->requestPost('parent_id');
            
            if ($parent_id <= 0) {
                throw new \Exception('Невалиден parent_id');
            }

            // Зареждане на модела за категории
            $this->loadModelAs('catalog/category', 'categories');
            $this->loadModelAs('catalog/product', 'products');

            // Получаване на подкатегориите
            $filter_data = [
                'filter_parent_id' => $parent_id,
                'sort' => 'c.sort_order',
                'order' => 'ASC'
            ];

            $subcategories = $this->categories->getCategories($filter_data);

            // Подготовка на данните за отговора
            $response_data = [];
            foreach ($subcategories as $category) {
                // Рекурсивно смятане на продукти включително от подкатегории
                $product_count = $this->getProductCountForCategory($category['category_id']);

                // Проверка дали категорията има подкатегории
                $has_subcategories = $this->hasSubcategories($category['category_id']);

                // Ако има подкатегории, получаваме броя им
                $subcategory_count = 0;
                if ($has_subcategories) {
                    $subcategory_filter = ['filter_parent_id' => $category['category_id']];
                    $subcategory_count = $this->categories->getTotalCategories($subcategory_filter);
                }

                $status = $category['status'] == 1 ? 'Активна' : 'Неактивна';
                $status_class = $category['status'] == 1 ? 'status-active' : 'status-inactive';

                $response_data[] = [
                    'category_id' => $category['category_id'],
                    'name' => $category['name'],
                    'parent_id' => $category['parent_id'],
                    'sort_order' => $category['sort_order'],
                    'status' => $category['status'],
                    'status_text' => $status,
                    'status_class' => $status_class,
                    'product_count' => $product_count,
                    'has_subcategories' => $has_subcategories,
                    'subcategory_count' => $subcategory_count,
                    'level' => $this->getCategoryLevel($category['category_id']),
                    'edit' => $this->getAdminLink('catalog/category/edit', 'category_id=' . $category['category_id']),
                    'delete' => $this->getAdminLink('catalog/category/delete', 'category_id=' . $category['category_id']),
                    'products_url' => $this->getAdminLink('catalog/product', 'filter_category_id=' . $category['category_id'])
                ];
            }

            // Връщане на JSON отговор
            $this->jsonResponse([
                'success' => true,
                'subcategories' => $response_data,
                'parent_id' => $parent_id,
                'count' => count($response_data)
            ]);

        } catch (\Exception $e) {
            $this->logDev('Грешка при зареждане на подкатегории: ' . $e->getMessage());
            
            $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Обновява sort_order на категория чрез AJAX с batch обновяване на всички подкатегории на същото ниво
     */
    public function updateSortOrder() {
        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            // Получаване на данните от заявката
            $category_id = (int)$this->requestPost('category_id');
            $new_sort_order = (int)$this->requestPost('sort_order');
            $parent_id = (int)$this->requestPost('parent_id');
            $new_parent_id = (int)$this->requestPost('new_parent_id');
            $drop_position = $this->requestPost('drop_position');
            $target_category_id = (int)$this->requestPost('target_category_id');
            $sibling_categories = $this->requestPost('sibling_categories');

            F()->log->developer($this->requestPost(), __FILE__, __LINE__);

            $this->logDev("Получени данни за batch обновяване:");
            $this->logDev("Category ID: {$category_id}");
            $this->logDev("New sort order: {$new_sort_order}");
            $this->logDev("Parent ID: {$parent_id}");
            $this->logDev("New parent ID: {$new_parent_id}");
            $this->logDev("Drop position: {$drop_position}");
            $this->logDev("Target category ID: {$target_category_id}");
            $this->logDev("Sibling categories: " . json_encode($sibling_categories));

            if ($category_id <= 0) {
                throw new \Exception('Невалиден category_id');
            }

            // Зареждане на модела за категории
            $this->loadModelAs('catalog/category', 'categories');

            // Проверка дали категорията съществува
            $category = $this->categories->getCategory($category_id);
            if (!$category) {
                throw new \Exception('Категорията не съществува');
            }

            // Изпълняване на batch обновяване на всички подкатегории на същото ниво
            $updated_categories = $this->performBatchSortOrderUpdate(
                $category_id,
                $parent_id,
                $new_parent_id,
                $drop_position,
                $target_category_id,
                $sibling_categories
            );

            $this->logDev("Batch обновяване завършено за " . count($updated_categories) . " категории");

            // Връщане на успешен отговор
            $this->jsonResponse([
                'success' => true,
                'message' => 'Позицията е обновена успешно',
                'category_id' => $category_id,
                'updated_categories' => $updated_categories,
                'batch_count' => count($updated_categories)
            ]);

        } catch (\Exception $e) {
            $this->logDev('Грешка при обновяване на sort_order: ' . $e->getMessage());
            
            $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Получава информация за категория чрез AJAX
     */
    public function getCategoryInfo() {
        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $category_id = (int)$this->requestPost('category_id');
            
            if ($category_id <= 0) {
                throw new \Exception('Невалиден category_id');
            }

            // Зареждане на модела за категории
            $this->loadModelAs('catalog/category', 'categories');
            $this->loadModelAs('catalog/product', 'products');

            // Получаване на информацията за категорията
            $category = $this->categories->getCategory($category_id);
            if (!$category) {
                throw new \Exception('Категорията не съществува');
            }

            $product_count = $this->products->getTotalProducts(['filter_category_id' => $category_id]);
            $has_subcategories = $this->hasSubcategories($category_id);

            // Подготовка на отговора
            $response_data = [
                'category_id' => $category['category_id'],
                'name' => $category['name'],
                'parent_id' => $category['parent_id'],
                'sort_order' => $category['sort_order'],
                'status' => $category['status'],
                'status_text' => $category['status'] ? 'Активна' : 'Неактивна',
                'status_class' => $category['status'] ? 'status-active' : 'status-inactive',
                'product_count' => $product_count,
                'has_subcategories' => $has_subcategories,
                'level' => $this->getCategoryLevel($category_id),
                'edit_url' => $this->getAdminLink('catalog/category/edit', 'category_id=' . $category_id),
                'products_url' => $this->getAdminLink('catalog/product', 'filter_category_id=' . $category_id)
            ];

            $this->jsonResponse([
                'success' => true,
                'data' => $response_data
            ]);

        } catch (\Exception $e) {
            $this->logDev('Грешка при получаване на информация за категория: ' . $e->getMessage());
            
            $this->jsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Проверява дали категорията има подкатегории
     *
     * @param int $category_id ID на категорията
     * @return bool True ако има подкатегории
     */
    private function hasSubcategories($category_id) {
        $subcategories = $this->categories->getCategories([
            'filter_parent_id' => $category_id,
            'start' => 0,
            'limit' => 1
        ]);

        return !empty($subcategories);
    }

    /**
     * Получава нивото на категорията в йерархията
     *
     * @param int $category_id ID на категорията
     * @return int Ниво на категорията
     */
    private function getCategoryLevel($category_id) {
        $path = $this->categories->getCategoryPath($category_id);
        return count($path) - 1; // Минус 1, защото включва и самата категория
    }

    /**
     * Проверява дали заявката е AJAX
     *
     * @return bool True ако е AJAX заявка
     */
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    }

    /**
     * Връща JSON отговор
     *
     * @param array $data Данни за отговора
     */
    private function jsonResponse($data) {
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Получава броя продукти в дадена категория (рекурсивно включва подкатегории)
     *
     * @param int $category_id ID на категорията
     * @return int Брой продукти включително от подкатегории
     */
    private function getProductCountForCategory($category_id) {
        // Получаваме всички подкатегории рекурсивно
        $all_category_ids = $this->getAllSubcategoryIds($category_id);

        // Добавяме и самата категория
        $all_category_ids[] = $category_id;

        // Смятаме общия брой продукти от всички категории
        $total_products = 0;
        foreach ($all_category_ids as $cat_id) {
            $total_products += $this->products->getTotalProducts(['filter_category_id' => $cat_id]);
        }

        return $total_products;
    }

    /**
     * Получава всички ID-та на подкатегории рекурсивно
     *
     * @param int $parent_id ID на родителската категория
     * @return array Масив с всички ID-та на подкатегории
     */
    private function getAllSubcategoryIds($parent_id) {
        $all_ids = [];

        // Получаваме директните подкатегории
        $subcategories = $this->categories->getCategories([
            'filter_parent_id' => $parent_id
        ]);

        foreach ($subcategories as $subcategory) {
            $subcategory_id = $subcategory['category_id'];
            $all_ids[] = $subcategory_id;

            // Рекурсивно получаваме подкатегориите на тази подкатегория
            $nested_ids = $this->getAllSubcategoryIds($subcategory_id);
            $all_ids = array_merge($all_ids, $nested_ids);
        }

        return $all_ids;
    }

    /**
     * Изпълнява batch обновяване на sort_order за всички подкатегории на същото ниво
     */
    private function performBatchSortOrderUpdate($moved_category_id, $parent_id, $new_parent_id, $drop_position, $target_category_id, $sibling_categories) {
        $updated_categories = [];

        F()->log->developer($sibling_categories, __FILE__, __LINE__);

        try {
            // Получаваме всички категории на същото ниво от базата данни
            $current_siblings = $this->getCurrentSiblingCategories($parent_id);

            // Изчисляваме новите sort_order стойности
            $new_sort_orders = $this->calculateNewSortOrders(
                $moved_category_id,
                $target_category_id,
                $drop_position,
                $current_siblings
            );

            // Обновяваме sort_order за всички засегнати категории
            foreach ($new_sort_orders as $category_id => $new_sort_order) {
                // $this->updateCategorySortOrderInDB($category_id, $new_sort_order);

                $updated_categories[] = [
                    'category_id' => $category_id,
                    'new_sort_order' => $new_sort_order
                ];

                $this->logDev("Обновен sort_order за категория {$category_id}: {$new_sort_order}");
            }

        } catch (\Exception $e) {
            $this->logDev('Грешка при batch обновяване: ' . $e->getMessage());
            throw $e;
        }

        return $updated_categories;
    }

    /**
     * Получава всички категории на същото ниво от базата данни
     */
    private function getCurrentSiblingCategories($parent_id) {
        $sql = "
            SELECT category_id, sort_order, parent_id
            FROM " . DB_PREFIX . "category
            WHERE parent_id = {$parent_id}
            ORDER BY sort_order ASC, category_id ASC
        ";

        $result = $this->db->query($sql);
        return $result->rows;
    }

    /**
     * Изчислява новите sort_order стойности за всички категории
     */
    private function calculateNewSortOrders($moved_category_id, $target_category_id, $drop_position, $current_siblings) {
        $new_sort_orders = [];
        $sort_index = 1;

        foreach ($current_siblings as $sibling) {
            $category_id = $sibling['category_id'];

            // Пропускаме преместената категория в първия цикъл
            if ($category_id == $moved_category_id) {
                continue;
            }

            // Ако сме достигнали target категорията и позицията е "before"
            if ($category_id == $target_category_id && $drop_position === 'before') {
                $new_sort_orders[$moved_category_id] = $sort_index++;
            }

            // Присвояваме sort_order на текущата категория
            $new_sort_orders[$category_id] = $sort_index++;

            // Ако сме достигнали target категорията и позицията е "after"
            if ($category_id == $target_category_id && $drop_position === 'after') {
                $new_sort_orders[$moved_category_id] = $sort_index++;
            }
        }

        // Ако преместената категория все още не е добавена (edge case)
        if (!isset($new_sort_orders[$moved_category_id])) {
            $new_sort_orders[$moved_category_id] = $sort_index;
        }

        return $new_sort_orders;
    }

    /**
     * Обновява sort_order на категория в базата данни
     */
    private function updateCategorySortOrderInDB($category_id, $sort_order) {
        $sql = "
            UPDATE " . DB_PREFIX . "category
            SET sort_order = {$sort_order}
            WHERE category_id = {$category_id}
        ";

        $this->db->query($sql);
    }

    /**
     * Логва съобщение за разработчици
     *
     * @param mixed $message Съобщението за логване
     */
    private function logDev($message) {
        F()->log->developer('[CATEGORY_AJAX] ' . print_r($message, true));
    }
}
