<!-- Categories Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">Категории</h1>
			<p class="text-gray-500 mt-1">Управление на категории и подкатегории на продукти</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ add_new_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-add-line"></i>
				</div>
				<span>Нова категория</span>
			</a>
		</div>
	</div>
</div>
<!-- Filters -->
<div class="bg-white border-b border-gray-200 px-6 py-3">
	<div class="flex flex-wrap items-center gap-4">
		<button id="filter-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
			<div class="w-5 h-5 flex items-center justify-center mr-2">
				<i class="ri-filter-3-line"></i>
			</div>
			<span>Филтър</span>
		</button>
		<div class="w-full md:w-auto">
			<div class="relative">

			</div>
		</div>
		<div class="w-full md:w-auto">
			<div class="relative">

			</div>
		</div>
		<!-- Filter Modal -->
		<div id="filter-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
			<div class="bg-white rounded-lg w-full max-w-md mx-4">
				<div class="flex justify-between items-center p-6 border-b border-gray-200">
					<h3 class="text-lg font-semibold text-gray-800">Филтър</h3>
					<button id="close-filter" class="text-gray-400 hover:text-gray-500">
						<div class="w-6 h-6 flex items-center justify-center">
							<i class="ri-close-line"></i>
						</div>
					</button>
				</div>
				<div class="p-6">
					<form id="filter-form" class="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
							<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
								<option value="">Всички</option>
								<option value="active">Активни</option>
								<option value="inactive">Неактивни</option>
							</select>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Тип категория</label>
							<select class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm pr-8">
								<option value="">Всички</option>
								<option value="main">Основни категории</option>
								<option value="sub">Подкатегории</option>
							</select>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Брой продукти</label>
							<div class="flex space-x-2">
								<input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="От">
								<input type="number" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm" placeholder="До">
							</div>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Дата на създаване</label>
							<div class="flex space-x-2">
								<input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
								<input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary text-sm">
							</div>
						</div>
						<div class="flex justify-end space-x-2 mt-6">
							<button type="button" id="reset-filter" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 text-sm whitespace-nowrap">Изчисти</button>
							<button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 text-sm whitespace-nowrap">Приложи филтър</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<!-- Categories List -->
	<div class="bg-white rounded shadow overflow-hidden">
		<div class="p-6 space-y-4">
			{% if categories %}
				{% for category in categories %}
					<div class="category-item relative flex items-center bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all"
						 data-category-id="{{ category.category_id }}"
						 data-parent-id="{{ category.parent_id }}"
						 data-sort-order="{{ category.sort_order }}"
						 data-level="{{ category.level }}"
						 {% if category.has_subcategories %}data-has-subcategories="true"{% endif %}>



						<div class="drag-handle flex items-center justify-center w-12 h-12 text-gray-400 cursor-grab">
							<div class="w-5 h-5 flex items-center justify-center">
								<i class="ri-drag-move-2-line"></i>
							</div>
						</div>
						<div class="flex-1 flex items-center py-3">
							<div class="flex-1">
								<div class="flex items-center">
									<h3 class="text-base font-medium text-gray-800">{{ category.name }}</h3>
									<span class="status-badge status-{{ category.status ? 'active' : 'inactive' }} ml-3">{{ category.status_text }}</span>
								</div>
								<div class="flex items-center mt-1 text-sm text-gray-500">
									<span>{{ category.product_count }} продукта</span>
									{% if category.has_subcategories %}
									<span class="ml-2">• {{ category.subcategory_count }} подкатегории</span>
									{% endif %}
									<a href="{{ category.products_url }}" class="text-primary hover:text-primary/80 ml-2">Виж продуктите</a>
								</div>
							</div>
							<div class="flex items-center space-x-2 pr-4">
								{% if category.has_subcategories %}
								<button class="category-expand-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full"
										data-category-id="{{ category.category_id }}"
										data-expanded="false"
										title="Покажи подкатегории">
									<div class="w-5 h-5 flex items-center justify-center">
										<i class="ri-arrow-right-s-line"></i>
									</div>
								</button>
								{% endif %}
								<a href="{{ category.edit }}" class="w-8 h-8 flex items-center justify-center text-gray-600 hover:bg-gray-100 rounded-full">
									<div class="w-5 h-5 flex items-center justify-center">
										<i class="ri-edit-line"></i>
									</div>
								</a>
								<button class="w-8 h-8 flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full"
										title="Изтриване" onclick="confirmDelete('{{ category.delete }}')">
									<div class="w-5 h-5 flex items-center justify-center">
										<i class="ri-delete-bin-line"></i>
									</div>
								</button>
							</div>
						</div>
					</div>
				{% endfor %}
			{% else %}
				<div class="text-center py-8">
					<div class="text-gray-400 mb-2">
						<i class="ri-folder-open-line text-4xl"></i>
					</div>
					<p class="text-gray-500">Няма намерени категории</p>
				</div>
			{% endif %}
		</div>
	</div>
</main>

<!-- CSS стилове за drag & drop функционалност -->
<style>
.drag-ready {
    cursor: grab !important;
}

.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.drop-zone-active {
    background-color: #f0f9ff;
    border-color: #0ea5e9;
}

.drop-zone-hover {
    background-color: #e0f2fe;
}

.drop-indicator {
    position: absolute;
    background-color: #0ea5e9;
    z-index: 1000;
}

.drop-indicator-before {
    top: -2px;
    left: 0;
    right: 0;
    height: 4px;
}

.drop-indicator-after {
    bottom: -2px;
    left: 0;
    right: 0;
    height: 4px;
}

/* Стилове за подкатегории */
.subcategory-item {
    margin-top: 8px;
    margin-bottom: 4px;
    background-color: #ffffff !important;
    position: relative;
}

.subcategory-item:hover {
    background-color: #f9fafb !important;
}

/* Визуални линии за дървовидна структура */
.subcategory-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: -20px;
    bottom: 0;
    width: 1px;
    background-color: #d1d5db;
    z-index: 1;
}

.subcategories-container .subcategory-item:last-child::before {
    height: 78%;
}

.subcategory-item::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 20px;
    height: 1px;
    background-color: #d1d5db;
    z-index: 1;
}

/* Линии за различни нива на вложеност */
.subcategory-item[data-level="1"]::before {
    left: -16px;
}

.subcategory-item[data-level="1"]::after {
    left: -16px;
    width: 16px;
}

.subcategory-item[data-level="2"]::before {
    left: -32px;
}

.subcategory-item[data-level="2"]::after {
    left: -32px;
    width: 16px;
}

.subcategory-item[data-level="3"]::before {
    left: -48px;
}

.subcategory-item[data-level="3"]::after {
    left: -48px;
    width: 16px;
}

/* Анимации за expand/collapse на подкатегории */
.subcategories-container {
    transition: all 0.3s ease-in-out;
}

.subcategories-container.expanding {
    animation: expandSubcategories 0.3s ease-out forwards;
}

.subcategories-container.collapsing {
    animation: collapseSubcategories 0.3s ease-in forwards;
}

@keyframes expandSubcategories {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 1000px;
        opacity: 1;
    }
}

@keyframes collapseSubcategories {
    from {
        max-height: 1000px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

/* Status badge стилове */
.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-inactive {
    background-color: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.drop-indicator-inside {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed #0ea5e9;
    background-color: rgba(14, 165, 233, 0.1);
}

/* Hierarchy Styles */
.subcategories-container {
    margin-left: 2rem;
}

.subcategory-item {
    padding-left: 1rem;
}

.category-expand-btn {
    transition: transform 0.2s ease;
}

.category-expand-btn[data-expanded="true"] i {
    transform: rotate(90deg);
}

/* Animation for expand/collapse */
@keyframes expandCategory {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 1000px;
        opacity: 1;
    }
}

@keyframes collapseCategory {
    from {
        max-height: 1000px;
        opacity: 1;
    }
    to {
        max-height: 0;
        opacity: 0;
    }
}

.subcategories-container.expanding {
    animation: expandCategory 0.3s ease-out;
}

.subcategories-container.collapsing {
    animation: collapseCategory 0.3s ease-in;
}

.category-connector::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    width: 16px;
    height: 1px;
    background-color: #d1d5db;
}

.category-connector::after {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    width: 1px;
    height: 50%;
    background-color: #d1d5db;
}
</style>

<!-- JavaScript -->
<script>
// Глобални променливи за AJAX URL адреси
window.categoryAjaxUrls = {
    loadSubcategories: '{{ ajax_load_subcategories }}',
    updateSortOrder: '{{ ajax_update_sort_order }}',
    getCategoryInfo: '{{ ajax_get_category_info }}'
};

// Функция за потвърждение на изтриване
function confirmDelete(deleteUrl) {
    if (confirm('Сигурни ли сте, че искате да изтриете тази категория?')) {
        window.location.href = deleteUrl;
    }
}
</script>
