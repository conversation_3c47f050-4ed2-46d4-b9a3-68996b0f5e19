/* Основни стилове за административния панел */
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
  font-family: 'Exo 2', sans-serif;
  background-color: #ffffff;
  font-size: 20px;
}
.search-input:focus {
  box-shadow: 0 0 0 2px rgba(110, 65, 180, 0.2);
}
.card-stats {
  transition: all 0.3s ease;
}
.card-stats:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Странична лента */
.sidebar-item:hover {
    background-color: rgba(110, 65, 180, 0.1);
}

.sidebar-item.active {
    background-color: rgba(110, 65, 180, 0.15);
    border-left: 3px solid #6e41b4;
}

/* Табове */
.tab-button {
    position: relative;
}

.tab-button.active {
    color: #6e41b4;
    font-weight: 500;
}

.tab-button.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #6e41b4;
}

/* Икони */
:where([class^="ri-"])::before {
    content: "\f3c2";
}

/* Чекбокс стилове */
input[type="checkbox"] {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
}

input[type="checkbox"]:checked {
    background-color: #6e41b4;
    border-color: #6e41b4;
}

input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Статус баджове */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-new {
    background-color: #e0f2fe;
    color: #0369a1;
}

.status-processing {
    background-color: #fef3c7;
    color: #92400e;
}

.status-completed {
    background-color: #dcfce7;
    color: #166534;
}

.status-cancelled {
    background-color: #fee2e2;
    color: #b91c1c;
}

.status-sent {
    background-color: #e0f2fe;
    color: #0369a1;
}

.status-rejected {
    background-color: #fee2e2;
    color: #b91c1c;
}

.status-failed {
    background-color: #fef2f2;
    color: #991b1b;
}

.status-returned-payment {
    background-color: #fef3c7;
    color: #92400e;
}

.status-returned {
    background-color: #f3e8ff;
    color: #7c3aed;
}

.status-processed {
    background-color: #dcfce7;
    color: #166534;
}

.status-reset {
    background-color: #f1f5f9;
    color: #475569;
}

.status-card-payed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-card-rejected {
    background-color: #fecaca;
    color: #dc2626;
}

.status-delayed {
    background-color: #fef3c7;
    color: #d97706;
}

/* Превключвател (toggle switch) */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #6e41b4;
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Селект полета */
.custom-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
}

/* Карти със статистики */

/* Модални прозорци */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
    overflow-y: auto;
}

.modal-content {
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Селектор за дата */
.date-picker::-webkit-calendar-picker-indicator {
    filter: invert(0.5);
}

/* Звездна оценка */
.star-rating {
    display: inline-flex;
}

.star-rating input {
    display: none;
}

.star-rating label {
    color: #ddd;
    cursor: pointer;
    font-size: 24px;
    padding: 0 2px;
    transition: color 0.2s;
}

.star-rating input:checked ~ label {
    color: #ffb800;
}

.star-rating label:hover,
.star-rating label:hover ~ label {
    color: #ffb800;
}

.star-display {
    color: #ddd;
}

.star-display.active {
    color: #ffb800;
}

.review-row:hover {
    background-color: rgba(110, 65, 180, 0.05);
}

/* Пагинация */
.pagination-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    transition: all 0.2s;
}

.pagination-button.active {
    background-color: #6e41b4;
    color: white;
}

.pagination-button:not(.active):hover {
    background-color: rgba(110, 65, 180, 0.1);
}

/* Радио бутони */
.radio-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    cursor: pointer;
}

.radio-container input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.radio-checkmark {
    position: relative;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.radio-container:hover input ~ .radio-checkmark {
    border-color: #6e41b4;
}

.radio-container input:checked ~ .radio-checkmark {
    background-color: #fff;
    border-color: #6e41b4;
}

.radio-checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.radio-container input:checked ~ .radio-checkmark:after {
    display: block;
}

.radio-container .radio-checkmark:after {
    top: 3px;
    left: 3px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #6e41b4;
}


input[type="checkbox"] {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    position: relative;
    }
    input[type="checkbox"]:checked {
    background-color: #6e41b4;
    border-color: #6e41b4;
    }
    input[type="checkbox"]:checked::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    }
    .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    }
    .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    }
    .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
    }
    .toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    }
    input:checked + .toggle-slider {
    background-color: #6e41b4;
    }
    input:checked + .toggle-slider:before {
    transform: translateX(20px);
    }
    .custom-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236B7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
    }
    .color-picker {
    appearance: none;
    width: 2.5rem;
    height: 2.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    }
    .color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    }
    .color-picker::-webkit-color-swatch {
    border: none;
    border-radius: 8px;
    }
    .theme-preview {
    transition: all 0.3s ease;
    }
    .font-preview {
    transition: font-family 0.3s ease;
    }
    .slider {
    appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 5px;
    background: #e5e7eb;
    outline: none;
    }
    .slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #6e41b4;
    cursor: pointer;
    }
    .slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #6e41b4;
    cursor: pointer;
    }
    .tab-button.active {
    color: #6e41b4;
    border-bottom: 2px solid #6e41b4;
    }
    .theme-option.selected {
    border-color: #6e41b4;
    box-shadow: 0 0 0 2px rgba(110, 65, 180, 0.2);
    }
    .font-option.selected {
    border-color: #6e41b4;
    background-color: rgba(110, 65, 180, 0.1);
    }
    .button-style.selected {
    border-color: #6e41b4;
    box-shadow: 0 0 0 2px rgba(110, 65, 180, 0.2);
    }
    .color-palette-item {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    }
    .color-palette-item.selected {
    outline: 2px solid #6e41b4;
    outline-offset: 2px;
    }
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
    }
    input[type="number"] {
    -moz-appearance: textfield;
    }
    .segmented-control {
    display: inline-flex;
    background-color: #f3f4f6;
    border-radius: 9999px;
    padding: 0.25rem;
    }
    .segmented-control-option {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    }
    .segmented-control-option.active {
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .product-card {
    transition: all 0.3s ease;
    }
    .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
    .product-card .card-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
    }
    .product-card:hover .card-actions {
    opacity: 1;
    }
        
    #product-category-autocomplete {
        position: relative;
        width: 100%;
    }
    
    .autocomplete-suggestions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        background-color: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 50;
        margin-top: 0.25rem;
    }
    
    .autocomplete-suggestion {
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: background-color 0.2s;
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    
    .autocomplete-suggestion:hover {
        background-color: #f3f4f6;
    }
    
    .autocomplete-loading,
    .autocomplete-no-results {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        padding: 0.75rem 1rem;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        font-size: 0.875rem;
        color: #6b7280;
        z-index: 50;
        margin-top: 0.25rem;
        text-align: center;
    }
    
    .autocomplete-no-results {
        color: #6b7280;
        font-style: italic;
    }
    
    .autocomplete-suggestion.active {
        background-color: #e5e7eb;
    }

    /* Autocomplete container for related products */
    #product-related-autocomplete {
        position: relative;
        width: 100%;
    }

    .autocomplete-container {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 50;
        margin-top: 0.25rem;
    }

/* Order Actions Dropdown стилове */
.order-actions-dropdown {
    position: absolute !important;
    right: 0 !important;
    top: 100% !important;
    margin-top: 0.25rem !important;
    width: 12rem !important;
    background-color: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    z-index: 9999 !important;
    display: block !important;
}

.order-actions-dropdown.hidden {
    display: none !important;
}

.order-actions-dropdown .py-1 {
    padding: 0.25rem 0 !important;
}

.order-actions-dropdown button {
    width: 100% !important;
    text-align: left !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
    color: #dc2626 !important;
    background-color: transparent !important;
    border: none !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    transition: background-color 0.2s !important;
}

.order-actions-dropdown button:hover {
    background-color: #fef2f2 !important;
}

.order-actions-dropdown button .mr-2 {
    margin-right: 0.5rem !important;
}

#status-dropdown li a, 
#period-dropdown li a,
#sort-dropdown li a
{
    font-size: 0.875rem !important;
}

button[data-tab] {
    font-size: 1rem !important;
}

.tab-content .label:not(.text-white) 
{
    font-size: 1rem !important;
    color: #6e41b4 !important;
    font-weight: 500 !important;
}



#tab-images.drag-over .image-upload-area {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
}

/* Drag & Drop стилове за категории */
.category-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.category-item.drop-zone-active {
    border-color: #3b82f6 !important;
    background-color: rgba(59, 130, 246, 0.05);
}

.category-item.drop-zone-hover {
    border-color: #1d4ed8 !important;
    background-color: rgba(29, 78, 216, 0.1);
    transform: scale(1.01);
}

/* Drop индикатори */
.drop-indicator {
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
    margin: 2px 0;
    opacity: 0.8;
    animation: pulse 1s infinite;
}

.drop-indicator-before {
    margin-bottom: 0;
}

.drop-indicator-after {
    margin-top: 0;
}

.drop-indicator-inside {
    position: absolute;
    top: 50%;
    left: 10px;
    right: 10px;
    height: 2px;
    margin: 0;
    background: rgba(59, 130, 246, 0.6);
    border: 1px dashed #3b82f6;
    border-radius: 4px;
}

/* Drag handle стилове */
.drag-handle {
    cursor: grab;
    transition: all 0.2s ease;
}

.drag-handle:hover {
    color: #3b82f6 !important;
    transform: scale(1.1);
}

.drag-handle:active {
    cursor: grabbing;
}

/* Анимации */
@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.image-upload-area {
    transition: all 0.2s ease-in-out;
}

/* Стилове за мениджъра на изображения */
.bg-secondary {
    background-color: #6b7280;
}

.bg-secondary:hover {
    background-color: #4b5563;
}

/* Responsive стилове за бутоните */
@media (max-width: 640px) {
    #tab-images .flex.items-center.space-x-4 {
        flex-direction: column;
        space-x: 0;
        gap: 0.75rem;
    }
}